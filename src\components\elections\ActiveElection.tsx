import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { 
  Vote,
  Users,
  Calendar,
  AlertCircle,
} from 'lucide-react-native';
import { ElectionCountdown } from './ElectionCountdown';
import { CandidateCard } from './CandidateCard';
import { showSuccessToast, showErrorToast } from '../../utils/toastUtils';

interface Candidate {
  id: string;
  userId: number;
  user: {
    id: number;
    username: string;
    level?: number;
  };
  platform?: string;
  votes: number;
  createdAt: string;
}

interface Election {
  id: string;
  stateId: string;
  state?: {
    id: string;
    name: string;
  };
  status: 'active' | 'completed' | 'cancelled';
  startDate: string;
  endDate: string;
  candidates: Candidate[];
  totalVotes: number;
  winner?: Candidate;
  userHasVoted: boolean;
  userVote?: {
    candidateId: string;
    candidate: Candidate;
  };
  createdAt: string;
  updatedAt: string;
}

interface ActiveElectionProps {
  election: Election;
  onVote?: (electionId: string, candidateId: string) => Promise<void>;
  onRefresh?: () => void;
}

export const ActiveElection: React.FC<ActiveElectionProps> = ({
  election,
  onVote,
  onRefresh,
}) => {
  const [voting, setVoting] = useState(false);

  const handleVote = async (candidateId: string) => {
    if (!onVote || voting) return;

    setVoting(true);
    try {
      await onVote(election.id, candidateId);
      showSuccessToast('Vote submitted successfully!');
      if (onRefresh) {
        onRefresh();
      }
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setVoting(false);
    }
  };

  const sortedCandidates = [...election.candidates].sort((a, b) => b.votes - a.votes);
  const winner = sortedCandidates[0];

  return (
    <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
      {/* Header */}
      <View className="flex-row items-center mb-4">
        <Vote width={24} height={24} color="#00d4ff" />
        <Text className="text-xl font-bold text-white ml-3">
          Active Election
        </Text>
      </View>

      {/* State Info */}
      {election.state && (
        <View className="bg-gray-700 p-3 rounded-lg mb-4">
          <Text className="text-gray-400 text-sm">State</Text>
          <Text className="text-white font-medium">{election.state.name}</Text>
        </View>
      )}

      {/* Election Stats */}
      <View className="flex-row justify-between mb-4">
        <View className="bg-gray-700 p-3 rounded-lg flex-1 mr-2">
          <View className="flex-row items-center mb-1">
            <Users width={16} height={16} color="#8b5cf6" />
            <Text className="text-gray-400 text-sm ml-2">Candidates</Text>
          </View>
          <Text className="text-white font-bold text-lg">
            {election.candidates.length}
          </Text>
        </View>
        
        <View className="bg-gray-700 p-3 rounded-lg flex-1 ml-2">
          <View className="flex-row items-center mb-1">
            <Vote width={16} height={16} color="#10b981" />
            <Text className="text-gray-400 text-sm ml-2">Total Votes</Text>
          </View>
          <Text className="text-white font-bold text-lg">
            {election.totalVotes}
          </Text>
        </View>
      </View>

      {/* Election Dates */}
      <View className="bg-gray-700 p-3 rounded-lg mb-4">
        <View className="flex-row items-center mb-2">
          <Calendar width={16} height={16} color="#60a5fa" />
          <Text className="text-gray-400 text-sm ml-2">Election Period</Text>
        </View>
        <Text className="text-white text-sm">
          Started: {new Date(election.startDate).toLocaleDateString()}
        </Text>
        <Text className="text-white text-sm">
          Ends: {new Date(election.endDate).toLocaleDateString()} at{' '}
          {new Date(election.endDate).toLocaleTimeString()}
        </Text>
      </View>

      {/* Countdown */}
      <View className="mb-4">
        <ElectionCountdown 
          endDate={election.endDate}
          onExpired={onRefresh}
        />
      </View>

      {/* User Vote Status */}
      {election.userHasVoted && election.userVote && (
        <View className="bg-green-900 bg-opacity-20 border border-green-400 border-opacity-30 rounded-lg p-4 mb-4">
          <View className="flex-row items-center">
            <Vote width={20} height={20} color="#10b981" />
            <Text className="text-green-400 font-medium ml-2">
              You voted for {election.userVote.candidate.user.username}
            </Text>
          </View>
        </View>
      )}

      {/* No Candidates Warning */}
      {election.candidates.length === 0 && (
        <View className="bg-yellow-900 bg-opacity-20 border border-yellow-400 border-opacity-30 rounded-lg p-4 mb-4">
          <View className="flex-row items-center">
            <AlertCircle width={20} height={20} color="#fbbf24" />
            <Text className="text-yellow-400 font-medium ml-2">
              No candidates have registered for this election yet.
            </Text>
          </View>
        </View>
      )}

      {/* Candidates */}
      {election.candidates.length > 0 && (
        <View>
          <Text className="text-lg font-bold text-white mb-4">
            Candidates ({election.candidates.length})
          </Text>
          
          {voting && (
            <View className="flex-row items-center justify-center py-4">
              <ActivityIndicator size="small" color="#00d4ff" />
              <Text className="text-neonBlue ml-2">Submitting vote...</Text>
            </View>
          )}
          
          <ScrollView className="max-h-96">
            {sortedCandidates.map((candidate, index) => (
              <CandidateCard
                key={candidate.id}
                candidate={candidate}
                totalVotes={election.totalVotes}
                userHasVoted={election.userHasVoted}
                userVotedFor={election.userVote?.candidateId}
                isWinner={election.status === 'completed' && candidate.id === winner?.id}
                onVote={election.status === 'active' ? handleVote : undefined}
                disabled={voting}
              />
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};
