import { api } from './api';
import { Factory, FactoryType, WageType, WorkSession } from '../types/factory';

export interface CreateFactoryDto {
  name: string;
  type: FactoryType;
  regionId: string;
  wage: number;
  wageType: WageType.PERCENTAGE;
}

export interface WorkAtFactoryDto {
  energySpent: number;
}

export interface UpdateFactoryDto {
  name?: string;
  wage?: number;
  resourcePerWork?: number;
  energyCost?: number;
  maxWorkers?: number;
}

export interface UpdateWorkerWageDto {
  workerId: number;
  wage: number;
}

export interface AutoWorkDto {
  enable: boolean;
}

export const factoryService = {
  // Get all factories
  getAllFactories: async (): Promise<Factory[]> => {
    const response = await api.get('/factories');
    return response.data;
  },

  // Get a single factory by ID
  getFactory: async (id: number): Promise<Factory> => {
    const response = await api.get(`/factories/${id}`);
    return response.data;
  },

  // Create a new factory
  createFactory: async (factoryData: CreateFactoryDto): Promise<Factory> => {
    const response = await api.post('/factories', factoryData);
    return response.data;
  },

  // Work at a factory
  workAtFactory: async (id: number, energySpent: number): Promise<WorkSession> => {
    const response = await api.post(`/factories/${id}/work`, { energySpent });
    return response.data;
  },

  // Get work history
  getWorkHistory: async (): Promise<WorkSession[]> => {
    const response = await api.get('/factories/work-history');
    return response.data;
  },

  // Get factories in user's region
  getFactoriesInUserRegion: async (): Promise<Factory[]> => {
    const response = await api.get('/factories/user-region');
    return response.data;
  },

  // Get factory workers
  getFactoryWorkers: async (id: number): Promise<any[]> => {
    const response = await api.get(`/factories/${id}/workers`);
    return response.data;
  },

  // Update a factory
  updateFactory: async (id: number, factoryData: UpdateFactoryDto): Promise<Factory> => {
    const response = await api.patch(`/factories/${id}`, factoryData);
    return response.data;
  },

  // Upgrade factory
  upgradeFactory: async (id: number): Promise<any> => {
    const response = await api.post(`/factories/${id}/upgrade`, { upgrade: true });
    return response.data;
  },

  // Update worker wage
  updateWorkerWage: async (factoryId: number, workerId: number, wageData: UpdateWorkerWageDto): Promise<any> => {
    const response = await api.post(`/factories/${factoryId}/workers/${workerId}/wage`, wageData);
    return response.data;
  },

  // Shutdown factory
  shutdownFactory: async (id: number): Promise<any> => {
    const response = await api.post(`/factories/${id}/shutdown`, { shutdown: true });
    return response.data;
  },

  // Fire worker
  fireWorker: async (factoryId: number, workerId: number): Promise<any> => {
    const response = await api.post(`/factories/${factoryId}/workers/${workerId}/fire`, { fire: true });
    return response.data;
  },

  // Enable or disable auto work mode for a factory
  setAutoWorkMode: async (id: number, autoWorkData: AutoWorkDto): Promise<any> => {
    const response = await api.post(`/factories/${id}/auto-work`, autoWorkData);
    return response.data;
  },

  // Get auto work status
  getAutoWorkStatus: async (): Promise<any> => {
    const response = await api.get('/factories/auto-work/status');
    return response.data;
  },
};

export default factoryService;