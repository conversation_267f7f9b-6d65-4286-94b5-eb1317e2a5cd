import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import {
  ArrowLeft,
  Crown,
  Users,
  UserPlus,
  UserMinus,
  Check,
  X,
  Edit2,
  Share2,
  Settings,
  AlertTriangle,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { partyService, Party, PartyJoinRequest } from '../services/partyService';
import { stateService } from '../services/stateService';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';
import PhotoUpload from '../components/PhotoUpload';

interface Props {
  navigation: any;
  route: any;
}

export const PartyDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  useAuthGuard({ navigation });
  
  const { partyId } = route.params;
  const { user } = useAuthStore();
  const [party, setParty] = useState<Party | null>(null);
  const [joinRequests, setJoinRequests] = useState<PartyJoinRequest[]>([]);
  const [userJoinRequest, setUserJoinRequest] = useState<PartyJoinRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isStateLeader, setIsStateLeader] = useState(false);
  
  // Modal states
  const [showRequestsModal, setShowRequestsModal] = useState(false);
  const [showTransferLeadershipModal, setShowTransferLeadershipModal] = useState(false);
  const [showLeavePartyModal, setShowLeavePartyModal] = useState(false);
  const [selectedNewLeader, setSelectedNewLeader] = useState<any>(null);
  
  // Editing states
  const [isEditingName, setIsEditingName] = useState(false);
  const [newName, setNewName] = useState('');
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false);

  useEffect(() => {
    loadPartyData();
    checkStateLeadership();
  }, [partyId]);

  const checkStateLeadership = async () => {
    try {
      const state = await stateService.getStateLedByUser();
      setIsStateLeader(!!state);
    } catch (error) {
      setIsStateLeader(false);
    }
  };

  const loadPartyData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [partyData, userRequests] = await Promise.all([
        partyService.getPartyById(partyId),
        partyService.getUserJoinRequests().catch(() => [])
      ]);
      
      setParty(partyData);
      setNewName(partyData.name);
      
      // Check if user has pending join request
      const pendingRequest = userRequests.find(
        (req: PartyJoinRequest) => req.partyId === partyId && req.status === 'pending'
      );
      setUserJoinRequest(pendingRequest || null);
      
      // If user is party leader, fetch join requests
      if (partyData.leaderId === user?.id) {
        const requests = await partyService.getPartyJoinRequests(partyId);
        setJoinRequests(requests);
      }
    } catch (error: any) {
      console.error('Error loading party data:', error);
      setError(error.message || 'Failed to load party details');
      showErrorToast('Failed to load party details');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadPartyData();
  };

  const handleJoinRequest = async () => {
    setActionLoading(true);
    try {
      const response = await partyService.createJoinRequest(partyId);
      setUserJoinRequest(response);
      showSuccessToast('Join request sent successfully!');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelRequest = async () => {
    if (!userJoinRequest) return;

    setActionLoading(true);
    try {
      await partyService.cancelJoinRequest(userJoinRequest.id);
      setUserJoinRequest(null);
      showSuccessToast('Join request cancelled successfully!');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleLeaveParty = () => {
    Alert.alert(
      'Leave Party',
      'Are you sure you want to leave this party?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Leave', style: 'destructive', onPress: confirmLeaveParty }
      ]
    );
  };

  const confirmLeaveParty = async () => {
    setActionLoading(true);
    try {
      await partyService.leaveParty(partyId);
      showSuccessToast('You have left the party.');
      navigation.goBack();
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleAcceptRequest = async (requestId: string) => {
    setActionLoading(true);
    try {
      await partyService.acceptJoinRequest(requestId);
      
      // Remove the request from the list
      setJoinRequests(prev => prev.filter(req => req.id !== requestId));
      
      // Reload party data to update member count
      await loadPartyData();
      
      showSuccessToast('Join request accepted!');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectRequest = async (requestId: string) => {
    setActionLoading(true);
    try {
      await partyService.rejectJoinRequest(requestId);
      
      // Remove the request from the list
      setJoinRequests(prev => prev.filter(req => req.id !== requestId));
      
      showSuccessToast('Join request rejected.');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleTransferLeadership = async (newLeaderId: number) => {
    if (!isPartyLeader || !newLeaderId) return;

    Alert.alert(
      'Transfer Leadership',
      'Are you sure you want to transfer leadership? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Transfer', style: 'destructive', onPress: () => confirmTransferLeadership(newLeaderId) }
      ]
    );
  };

  const confirmTransferLeadership = async (newLeaderId: number) => {
    setActionLoading(true);
    try {
      const updatedParty = await partyService.transferLeadership(partyId, newLeaderId);
      setParty(updatedParty);
      setShowTransferLeadershipModal(false);
      
      showSuccessToast('Leadership transferred successfully!');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleKickMember = async (memberId: number) => {
    Alert.alert(
      'Kick Member',
      'Are you sure you want to kick this member from the party?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Kick', style: 'destructive', onPress: () => confirmKickMember(memberId) }
      ]
    );
  };

  const confirmKickMember = async (memberId: number) => {
    setActionLoading(true);
    try {
      const updatedParty = await partyService.kickMember(partyId, memberId);
      setParty(updatedParty);
      
      showSuccessToast('Member kicked from party.');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateName = async () => {
    if (!newName.trim() || newName === party?.name) {
      setIsEditingName(false);
      return;
    }

    setIsUpdatingName(true);
    try {
      const updatedParty = await partyService.updateParty(partyId, { name: newName.trim() });
      setParty(updatedParty);
      setIsEditingName(false);
      
      showSuccessToast('Party name updated successfully!');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handlePhotoUpload = async (imageUri: string) => {
    setIsUploadingPhoto(true);
    try {
      const formData = new FormData();
      formData.append('picture', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'party-picture.jpg',
      } as any);
      
      const updatedParty = await partyService.uploadPartyPicture(partyId, formData);
      setParty(updatedParty);
      
      showSuccessToast('Party photo updated successfully!');
    } catch (error: any) {
      showErrorToast(error);
    } finally {
      setIsUploadingPhoto(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#00d4ff" />
        <Text className="text-neonBlue text-xl mt-4">Loading party details...</Text>
      </View>
    );
  }

  if (error || !party) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center p-4">
        <AlertTriangle width={48} height={48} color="#ef4444" />
        <Text className="text-white text-xl mt-4 text-center">
          {error || 'Party not found'}
        </Text>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mt-4 bg-blue-600 px-6 py-3 rounded-xl"
        >
          <Text className="text-white font-medium">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const isPartyLeader = party.leaderId === user?.id;
  const isPartyMember = party.members.some(member => member.id === user?.id);
  const canManageParty = isPartyLeader || isStateLeader;

  return (
    <View className="flex-1 bg-gray-900">
      {/* Header */}
      <View className="bg-gray-800 pt-12 pb-4 px-4 border-b border-gray-700">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="p-2"
          >
            <ArrowLeft width={24} height={24} color="#ffffff" />
          </TouchableOpacity>
          <Text className="text-white text-lg font-bold">Party Details</Text>
          <View className="w-8" />
        </View>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View className="p-4">
          {/* Party Header */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center mb-4">
              {/* Party Photo */}
              <View className="mr-4">
                {canManageParty ? (
                  <PhotoUpload
                    currentPhotoUrl={party.picture}
                    onPhotoUpload={handlePhotoUpload}
                    uploading={isUploadingPhoto}
                    size="lg"
                    placeholder={party.name.charAt(0).toUpperCase()}
                  />
                ) : (
                  <View className="w-20 h-20 bg-violet-900 rounded-xl items-center justify-center border border-violet-500">
                    {party.picture ? (
                      <Text>Photo</Text>
                    ) : (
                      <Text className="text-2xl text-violet-400">
                        {party.name.charAt(0).toUpperCase()}
                      </Text>
                    )}
                  </View>
                )}
              </View>

              {/* Party Info */}
              <View className="flex-1">
                {isEditingName ? (
                  <View className="flex-row items-center space-x-2">
                    <TextInput
                      value={newName}
                      onChangeText={setNewName}
                      className="flex-1 bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600"
                      placeholder="Party name"
                      placeholderTextColor="#9ca3af"
                    />
                    <TouchableOpacity
                      onPress={handleUpdateName}
                      disabled={isUpdatingName}
                      className="bg-green-600 p-2 rounded-lg"
                    >
                      {isUpdatingName ? (
                        <ActivityIndicator size="small" color="#ffffff" />
                      ) : (
                        <Check width={20} height={20} color="#ffffff" />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setIsEditingName(false);
                        setNewName(party.name);
                      }}
                      className="bg-red-600 p-2 rounded-lg"
                    >
                      <X width={20} height={20} color="#ffffff" />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View className="flex-row items-center justify-between">
                    <View>
                      <Text className="text-2xl font-bold text-white">{party.name}</Text>
                      <Text className="text-gray-400 text-sm">
                        Created {new Date(party.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    {canManageParty && (
                      <TouchableOpacity
                        onPress={() => setIsEditingName(true)}
                        className="p-2"
                      >
                        <Edit2 width={20} height={20} color="#9ca3af" />
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </View>
            </View>

            {/* Party Stats */}
            <View className="flex-row justify-between bg-gray-700 rounded-lg p-4">
              <View className="items-center">
                <Text className="text-2xl font-bold text-white">{party.memberCount}</Text>
                <Text className="text-gray-400 text-sm">Members</Text>
              </View>
              <View className="items-center">
                <Text className="text-2xl font-bold text-white">{party.maxMembers}</Text>
                <Text className="text-gray-400 text-sm">Max Members</Text>
              </View>
              <View className="items-center">
                <Text className="text-2xl font-bold text-white">
                  {party.region?.name || 'Unknown'}
                </Text>
                <Text className="text-gray-400 text-sm">Region</Text>
              </View>
            </View>
          </View>

          {/* Leader Section */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center mb-4">
              <Crown width={20} height={20} color="#fbbf24" />
              <Text className="text-xl font-bold text-white ml-2">Party Leader</Text>
            </View>
            
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="w-12 h-12 bg-gray-700 rounded-full items-center justify-center">
                  <Text className="text-lg text-white">
                    {party.leader?.username?.charAt(0).toUpperCase() || 'L'}
                  </Text>
                </View>
                <View className="ml-3">
                  <Text className="text-lg font-medium text-white">
                    {party.leader?.username || 'Unknown'}
                  </Text>
                  <Text className="text-gray-400 text-sm">
                    Level {party.leader?.level || 0}
                  </Text>
                </View>
              </View>
              
              {isPartyLeader && party.members.length > 1 && (
                <TouchableOpacity
                  onPress={() => setShowTransferLeadershipModal(true)}
                  className="bg-yellow-600 px-4 py-2 rounded-lg"
                >
                  <Text className="text-white font-medium text-sm">Transfer Leadership</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Members Section */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center justify-between mb-4">
              <View className="flex-row items-center">
                <Users width={20} height={20} color="#8b5cf6" />
                <Text className="text-xl font-bold text-white ml-2">
                  Members ({party.memberCount})
                </Text>
              </View>
              
              {isPartyLeader && joinRequests.length > 0 && (
                <TouchableOpacity
                  onPress={() => setShowRequestsModal(true)}
                  className="bg-blue-600 px-3 py-2 rounded-lg"
                >
                  <Text className="text-white font-medium text-sm">
                    Requests ({joinRequests.length})
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            
            <View className="space-y-3">
              {party.members.map((member) => (
                <View key={member.id} className="flex-row items-center justify-between bg-gray-700 p-3 rounded-lg">
                  <View className="flex-row items-center">
                    <View className="w-10 h-10 bg-gray-600 rounded-full items-center justify-center">
                      <Text className="text-white font-medium">
                        {member.username.charAt(0).toUpperCase()}
                      </Text>
                    </View>
                    <View className="ml-3">
                      <Text className="text-white font-medium">{member.username}</Text>
                      <Text className="text-gray-400 text-sm">Level {member.level || 0}</Text>
                    </View>
                  </View>
                  
                  {isPartyLeader && member.id !== user?.id && (
                    <TouchableOpacity
                      onPress={() => handleKickMember(member.id)}
                      className="bg-red-600 p-2 rounded-lg"
                    >
                      <UserMinus width={16} height={16} color="#ffffff" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Action Buttons */}
          <View className="space-y-4">
            {!isPartyMember && !userJoinRequest && (
              <TouchableOpacity
                onPress={handleJoinRequest}
                disabled={actionLoading}
                className="bg-green-600 py-4 rounded-xl"
              >
                <View className="flex-row items-center justify-center">
                  {actionLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <UserPlus width={20} height={20} color="#ffffff" />
                  )}
                  <Text className="text-white font-bold ml-2">
                    {actionLoading ? 'Sending Request...' : 'Request to Join'}
                  </Text>
                </View>
              </TouchableOpacity>
            )}

            {userJoinRequest && (
              <TouchableOpacity
                onPress={handleCancelRequest}
                disabled={actionLoading}
                className="bg-yellow-600 py-4 rounded-xl"
              >
                <View className="flex-row items-center justify-center">
                  {actionLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <X width={20} height={20} color="#ffffff" />
                  )}
                  <Text className="text-white font-bold ml-2">
                    {actionLoading ? 'Cancelling...' : 'Cancel Join Request'}
                  </Text>
                </View>
              </TouchableOpacity>
            )}

            {isPartyMember && !isPartyLeader && (
              <TouchableOpacity
                onPress={handleLeaveParty}
                disabled={actionLoading}
                className="bg-red-600 py-4 rounded-xl"
              >
                <View className="flex-row items-center justify-center">
                  {actionLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <UserMinus width={20} height={20} color="#ffffff" />
                  )}
                  <Text className="text-white font-bold ml-2">
                    {actionLoading ? 'Leaving...' : 'Leave Party'}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Join Requests Modal */}
      <Modal
        visible={showRequestsModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowRequestsModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-end">
          <View className="bg-gray-800 rounded-t-3xl p-6 max-h-96">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-xl font-bold text-white">Join Requests</Text>
              <TouchableOpacity onPress={() => setShowRequestsModal(false)}>
                <X width={24} height={24} color="#ffffff" />
              </TouchableOpacity>
            </View>

            <ScrollView className="max-h-80">
              {joinRequests.length === 0 ? (
                <Text className="text-gray-400 text-center py-8">No pending requests</Text>
              ) : (
                <View className="space-y-3">
                  {joinRequests.map((request) => (
                    <View key={request.id} className="bg-gray-700 p-4 rounded-lg">
                      <View className="flex-row items-center justify-between">
                        <View>
                          <Text className="text-white font-medium">
                            {request.user?.username || 'Unknown User'}
                          </Text>
                          <Text className="text-gray-400 text-sm">
                            Level {request.user?.level || 0}
                          </Text>
                          {request.message && (
                            <Text className="text-gray-300 text-sm mt-1">
                              "{request.message}"
                            </Text>
                          )}
                        </View>

                        <View className="flex-row space-x-2">
                          <TouchableOpacity
                            onPress={() => handleAcceptRequest(request.id)}
                            disabled={actionLoading}
                            className="bg-green-600 p-2 rounded-lg"
                          >
                            <Check width={16} height={16} color="#ffffff" />
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => handleRejectRequest(request.id)}
                            disabled={actionLoading}
                            className="bg-red-600 p-2 rounded-lg"
                          >
                            <X width={16} height={16} color="#ffffff" />
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Transfer Leadership Modal */}
      <Modal
        visible={showTransferLeadershipModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowTransferLeadershipModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-end">
          <View className="bg-gray-800 rounded-t-3xl p-6 max-h-96">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-xl font-bold text-white">Transfer Leadership</Text>
              <TouchableOpacity onPress={() => setShowTransferLeadershipModal(false)}>
                <X width={24} height={24} color="#ffffff" />
              </TouchableOpacity>
            </View>

            <Text className="text-gray-400 mb-4">
              Select a member to transfer leadership to:
            </Text>

            <ScrollView className="max-h-64">
              <View className="space-y-2">
                {party.members
                  .filter(member => member.id !== user?.id)
                  .map((member) => (
                    <TouchableOpacity
                      key={member.id}
                      onPress={() => handleTransferLeadership(member.id)}
                      disabled={actionLoading}
                      className="bg-gray-700 p-4 rounded-lg flex-row items-center justify-between"
                    >
                      <View className="flex-row items-center">
                        <View className="w-10 h-10 bg-gray-600 rounded-full items-center justify-center">
                          <Text className="text-white font-medium">
                            {member.username.charAt(0).toUpperCase()}
                          </Text>
                        </View>
                        <View className="ml-3">
                          <Text className="text-white font-medium">{member.username}</Text>
                          <Text className="text-gray-400 text-sm">Level {member.level || 0}</Text>
                        </View>
                      </View>

                      {actionLoading ? (
                        <ActivityIndicator size="small" color="#ffffff" />
                      ) : (
                        <Crown width={20} height={20} color="#fbbf24" />
                      )}
                    </TouchableOpacity>
                  ))}
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};
