import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { ArrowLeft, Shield } from 'lucide-react-native';

interface Props {
  navigation: any;
}

export const PrivacyPolicyScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <View className="flex-1 bg-gray-900">
      {/* Header */}
      <View className="bg-gray-800 pt-12 pb-4 px-4 border-b border-gray-700">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="p-2"
          >
            <ArrowLeft width={24} height={24} color="#ffffff" />
          </TouchableOpacity>
          <Text className="text-white text-lg font-bold">Privacy Policy</Text>
          <View className="w-8" />
        </View>
      </View>

      <ScrollView className="flex-1 p-4">
        <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <View className="flex-row items-center mb-6">
            <Shield width={24} height={24} color="#00d4ff" />
            <Text className="text-xl font-bold text-white ml-3">Privacy Policy</Text>
          </View>

          <View className="space-y-6">
            <View>
              <Text className="text-lg font-semibold text-white mb-2">Information We Collect</Text>
              <Text className="text-gray-300 leading-6">
                We collect information you provide directly to us, such as when you create an account, 
                update your profile, or communicate with us. This includes your username, email address, 
                and any other information you choose to provide.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">How We Use Your Information</Text>
              <Text className="text-gray-300 leading-6">
                We use the information we collect to provide, maintain, and improve our services, 
                process transactions, send you technical notices and support messages, and communicate 
                with you about products, services, and events.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Information Sharing</Text>
              <Text className="text-gray-300 leading-6">
                We do not sell, trade, or otherwise transfer your personal information to third parties 
                without your consent, except as described in this policy or as required by law.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Data Security</Text>
              <Text className="text-gray-300 leading-6">
                We implement appropriate security measures to protect your personal information against 
                unauthorized access, alteration, disclosure, or destruction.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Game Data</Text>
              <Text className="text-gray-300 leading-6">
                Your game progress, statistics, and in-game activities are stored to provide you with 
                a consistent gaming experience across devices and sessions.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Cookies and Tracking</Text>
              <Text className="text-gray-300 leading-6">
                We may use cookies and similar tracking technologies to enhance your experience, 
                analyze usage patterns, and improve our services.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Your Rights</Text>
              <Text className="text-gray-300 leading-6">
                You have the right to access, update, or delete your personal information. 
                You may also opt out of certain communications from us.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Children's Privacy</Text>
              <Text className="text-gray-300 leading-6">
                Our service is not intended for children under 13 years of age. We do not knowingly 
                collect personal information from children under 13.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Changes to This Policy</Text>
              <Text className="text-gray-300 leading-6">
                We may update this privacy policy from time to time. We will notify you of any 
                changes by posting the new policy on this page and updating the "Last Updated" date.
              </Text>
            </View>

            <View>
              <Text className="text-lg font-semibold text-white mb-2">Contact Us</Text>
              <Text className="text-gray-300 leading-6">
                If you have any questions about this privacy policy, please contact us through 
                the in-game support system or at our official support channels.
              </Text>
            </View>

            <View className="bg-gray-700 p-4 rounded-lg mt-6">
              <Text className="text-gray-400 text-sm text-center">
                Last Updated: {new Date().toLocaleDateString()}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};
