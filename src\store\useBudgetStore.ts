import { create } from 'zustand';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';

interface BudgetTransaction {
  id: string;
  stateId: string;
  type: 'income' | 'expense';
  category: string;
  amount: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

interface TaxConfiguration {
  id: string;
  stateId: string;
  incomeTaxRate: number;
  corporateTaxRate: number;
  salesTaxRate: number;
  propertyTaxRate: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface BudgetSummary {
  stateId: string;
  treasury: number;
  dailyIncome: number;
  dailyExpenses: number;
  netDailyChange: number;
  weeklyIncome: number;
  weeklyExpenses: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  lastUpdated: Date;
}

interface BudgetSummaryResponse {
  currentTreasury: number;
  dailyIncome: number;
  dailyExpenses: number;
}

interface GetBudgetTransactionsDto {
  page?: number;
  limit?: number;
  type?: 'income' | 'expense';
  category?: string;
  startDate?: string;
  endDate?: string;
}

interface UpdateTaxConfigurationDto {
  incomeTaxRate?: number;
  corporateTaxRate?: number;
  salesTaxRate?: number;
  propertyTaxRate?: number;
}

interface BudgetState {
  treasury: number;
  budgetSummary: BudgetSummary | null;
  taxConfig: TaxConfiguration | null;
  transactions: BudgetTransaction[];
  transactionsPagination: any;
  transactionsSummary: any;
  loading: {
    summary: boolean;
    taxConfig: boolean;
    transactions: boolean;
    updateTaxConfig: boolean;
  };
  error: {
    summary: string | null;
    taxConfig: string | null;
    transactions: string | null;
    updateTaxConfig: string | null;
  };
  lastUpdated: {
    summary: Date | null;
    taxConfig: Date | null;
    transactions: Date | null;
  };
}

interface BudgetStore extends BudgetState {
  fetchBudgetSummary: (stateId: string, force?: boolean) => Promise<void>;
  fetchTaxConfiguration: (stateId: string, force?: boolean) => Promise<void>;
  fetchBudgetTransactions: (stateId: string, params?: GetBudgetTransactionsDto, force?: boolean) => Promise<void>;
  updateTaxConfiguration: (stateId: string, config: UpdateTaxConfigurationDto) => Promise<boolean>;
  refreshAll: (stateId: string) => Promise<void>;
  clearError: (errorType: keyof BudgetState['error']) => void;
  reset: () => void;
}

const initialState: BudgetState = {
  treasury: 0,
  budgetSummary: null,
  taxConfig: null,
  transactions: [],
  transactionsPagination: null,
  transactionsSummary: null,
  loading: {
    summary: false,
    taxConfig: false,
    transactions: false,
    updateTaxConfig: false,
  },
  error: {
    summary: null,
    taxConfig: null,
    transactions: null,
    updateTaxConfig: null,
  },
  lastUpdated: {
    summary: null,
    taxConfig: null,
    transactions: null,
  },
};

export const useBudgetStore = create<BudgetStore>((set, get) => ({
  ...initialState,

  // Fetch budget summary
  fetchBudgetSummary: async (stateId: string, force = false) => {
    const state = get();
    
    // Return cached data if available and not forcing
    if (state.budgetSummary && !force && state.lastUpdated.summary) {
      const timeSinceUpdate = Date.now() - state.lastUpdated.summary.getTime();
      if (timeSinceUpdate < 5 * 60 * 1000) { // 5 minutes cache
        return;
      }
    }

    // If already loading and not forcing, return
    if (state.loading.summary && !force) return;

    set((state) => ({
      loading: { ...state.loading, summary: true },
      error: { ...state.error, summary: null },
    }));

    try {
      // Mock budget service call - replace with actual service when available
      const mockResponse: BudgetSummaryResponse = {
        currentTreasury: 10000,
        dailyIncome: 500,
        dailyExpenses: 200,
      };

      // Transform the API response to match our expected interface
      const summary: BudgetSummary = {
        stateId: stateId,
        treasury: mockResponse.currentTreasury || 0,
        dailyIncome: mockResponse.dailyIncome || 0,
        dailyExpenses: mockResponse.dailyExpenses || 0,
        netDailyChange: (mockResponse.dailyIncome || 0) - Math.abs(mockResponse.dailyExpenses || 0),
        weeklyIncome: 0, // API doesn't provide this yet
        weeklyExpenses: 0, // API doesn't provide this yet
        monthlyIncome: 0, // API doesn't provide this yet
        monthlyExpenses: 0, // API doesn't provide this yet
        lastUpdated: new Date()
      };

      set((state) => ({
        budgetSummary: summary,
        treasury: summary.treasury,
        loading: { ...state.loading, summary: false },
        lastUpdated: { ...state.lastUpdated, summary: new Date() },
      }));
    } catch (error: any) {
      console.error('Failed to fetch budget summary:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to fetch budget summary';
      set((state) => ({
        loading: { ...state.loading, summary: false },
        error: { ...state.error, summary: errorMessage },
      }));
      showErrorToast(errorMessage);
    }
  },

  fetchTaxConfiguration: async (stateId: string, force = false) => {
    const state = get();
    
    // Return cached data if available and not forcing
    if (state.taxConfig && !force && state.lastUpdated.taxConfig) {
      const timeSinceUpdate = Date.now() - state.lastUpdated.taxConfig.getTime();
      if (timeSinceUpdate < 10 * 60 * 1000) { // 10 minutes cache
        return;
      }
    }

    // If already loading and not forcing, return
    if (state.loading.taxConfig && !force) return;

    set((state) => ({
      loading: { ...state.loading, taxConfig: true },
      error: { ...state.error, taxConfig: null },
    }));

    try {
      // Mock tax config - replace with actual service when available
      const mockTaxConfig: TaxConfiguration = {
        id: '1',
        stateId: stateId,
        incomeTaxRate: 10,
        corporateTaxRate: 15,
        salesTaxRate: 8,
        propertyTaxRate: 2,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      set((state) => ({
        taxConfig: mockTaxConfig,
        loading: { ...state.loading, taxConfig: false },
        lastUpdated: { ...state.lastUpdated, taxConfig: new Date() },
      }));
    } catch (error: any) {
      console.error('Failed to fetch tax configuration:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to fetch tax configuration';
      set((state) => ({
        loading: { ...state.loading, taxConfig: false },
        error: { ...state.error, taxConfig: errorMessage },
      }));
      showErrorToast(errorMessage);
    }
  },

  fetchBudgetTransactions: async (stateId: string, params: GetBudgetTransactionsDto = {}, force = false) => {
    const state = get();

    // If already loading and not forcing, return
    if (state.loading.transactions && !force) return;

    set((state) => ({
      loading: { ...state.loading, transactions: true },
      error: { ...state.error, transactions: null },
    }));

    try {
      // Mock transactions - replace with actual service when available
      const mockTransactions: BudgetTransaction[] = [
        {
          id: '1',
          stateId: stateId,
          type: 'income',
          category: 'taxes',
          amount: 1000,
          description: 'Daily tax collection',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          stateId: stateId,
          type: 'expense',
          category: 'military',
          amount: 500,
          description: 'Military maintenance',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      set((state) => ({
        transactions: mockTransactions,
        loading: { ...state.loading, transactions: false },
        lastUpdated: { ...state.lastUpdated, transactions: new Date() },
      }));
    } catch (error: any) {
      console.error('Failed to fetch budget transactions:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to fetch budget transactions';
      set((state) => ({
        loading: { ...state.loading, transactions: false },
        error: { ...state.error, transactions: errorMessage },
      }));
      showErrorToast(errorMessage);
    }
  },

  updateTaxConfiguration: async (stateId: string, config: UpdateTaxConfigurationDto) => {
    set((state) => ({
      loading: { ...state.loading, updateTaxConfig: true },
      error: { ...state.error, updateTaxConfig: null },
    }));

    try {
      // Mock update - replace with actual service when available
      const updatedConfig: TaxConfiguration = {
        ...get().taxConfig!,
        ...config,
        updatedAt: new Date().toISOString(),
      };

      set((state) => ({
        taxConfig: updatedConfig,
        loading: { ...state.loading, updateTaxConfig: false },
      }));

      showSuccessToast('Tax configuration updated successfully');
      return true;
    } catch (error: any) {
      console.error('Failed to update tax configuration:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to update tax configuration';
      set((state) => ({
        loading: { ...state.loading, updateTaxConfig: false },
        error: { ...state.error, updateTaxConfig: errorMessage },
      }));
      showErrorToast(errorMessage);
      return false;
    }
  },

  refreshAll: async (stateId: string) => {
    await Promise.all([
      get().fetchBudgetSummary(stateId, true),
      get().fetchTaxConfiguration(stateId, true),
      get().fetchBudgetTransactions(stateId, {}, true),
    ]);
  },

  clearError: (errorType: keyof BudgetState['error']) => {
    set((state) => ({
      error: { ...state.error, [errorType]: null },
    }));
  },

  reset: () => {
    set(initialState);
  },
}));
