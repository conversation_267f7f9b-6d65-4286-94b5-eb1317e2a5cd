import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { 
  User, 
  Vote, 
  Crown,
  CheckCircle,
} from 'lucide-react-native';

interface Candidate {
  id: string;
  userId: number;
  user: {
    id: number;
    username: string;
    level?: number;
  };
  platform?: string;
  votes: number;
  createdAt: string;
}

interface CandidateCardProps {
  candidate: Candidate;
  totalVotes: number;
  userHasVoted: boolean;
  userVotedFor?: string;
  isWinner?: boolean;
  onVote?: (candidateId: string) => void;
  disabled?: boolean;
}

export const CandidateCard: React.FC<CandidateCardProps> = ({
  candidate,
  totalVotes,
  userHasVoted,
  userVotedFor,
  isWinner = false,
  onVote,
  disabled = false,
}) => {
  const votePercentage = totalVotes > 0 ? (candidate.votes / totalVotes) * 100 : 0;
  const isUserVote = userVotedFor === candidate.id;

  const handleVote = () => {
    if (!disabled && !userHasVoted && onVote) {
      onVote(candidate.id);
    }
  };

  return (
    <View className={`bg-gray-700 rounded-lg p-4 mb-3 border ${
      isWinner 
        ? 'border-yellow-400 bg-yellow-900 bg-opacity-20' 
        : isUserVote 
          ? 'border-green-400 bg-green-900 bg-opacity-20'
          : 'border-gray-600'
    }`}>
      {/* Header */}
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-row items-center">
          <View className="w-12 h-12 bg-gray-600 rounded-full items-center justify-center">
            <User width={20} height={20} color="#ffffff" />
          </View>
          <View className="ml-3">
            <View className="flex-row items-center">
              <Text className="text-lg font-bold text-white">
                {candidate.user.username}
              </Text>
              {isWinner && (
                <Crown width={16} height={16} color="#fbbf24" className="ml-2" />
              )}
              {isUserVote && (
                <CheckCircle width={16} height={16} color="#10b981" className="ml-2" />
              )}
            </View>
            <Text className="text-gray-400 text-sm">
              Level {candidate.user.level || 0}
            </Text>
          </View>
        </View>
        
        <View className="items-end">
          <Text className={`text-lg font-bold ${
            isWinner ? 'text-yellow-400' : 'text-white'
          }`}>
            {candidate.votes}
          </Text>
          <Text className="text-gray-400 text-sm">
            {candidate.votes === 1 ? 'vote' : 'votes'}
          </Text>
        </View>
      </View>

      {/* Platform */}
      {candidate.platform && (
        <View className="mb-3">
          <Text className="text-gray-300 text-sm leading-5">
            "{candidate.platform}"
          </Text>
        </View>
      )}

      {/* Vote Progress Bar */}
      <View className="mb-3">
        <View className="flex-row items-center justify-between mb-1">
          <Text className="text-gray-400 text-sm">Vote Share</Text>
          <Text className="text-gray-400 text-sm">{votePercentage.toFixed(1)}%</Text>
        </View>
        <View className="h-2 bg-gray-600 rounded-full overflow-hidden">
          <View
            className={`h-full ${
              isWinner 
                ? 'bg-yellow-400' 
                : isUserVote 
                  ? 'bg-green-400'
                  : 'bg-blue-400'
            }`}
            style={{ width: `${Math.max(votePercentage, 2)}%` }}
          />
        </View>
      </View>

      {/* Vote Button */}
      {!userHasVoted && onVote && (
        <TouchableOpacity
          onPress={handleVote}
          disabled={disabled}
          className={`py-3 rounded-lg ${
            disabled 
              ? 'bg-gray-600' 
              : 'bg-blue-600 active:bg-blue-700'
          }`}
        >
          <View className="flex-row items-center justify-center">
            <Vote width={16} height={16} color="#ffffff" />
            <Text className="text-white font-medium ml-2">
              {disabled ? 'Voting...' : 'Vote for this candidate'}
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {/* Already Voted Indicator */}
      {userHasVoted && isUserVote && (
        <View className="bg-green-900 bg-opacity-30 py-2 px-3 rounded-lg">
          <View className="flex-row items-center justify-center">
            <CheckCircle width={16} height={16} color="#10b981" />
            <Text className="text-green-400 font-medium ml-2">
              You voted for this candidate
            </Text>
          </View>
        </View>
      )}

      {/* Winner Badge */}
      {isWinner && (
        <View className="bg-yellow-900 bg-opacity-30 py-2 px-3 rounded-lg mt-2">
          <View className="flex-row items-center justify-center">
            <Crown width={16} height={16} color="#fbbf24" />
            <Text className="text-yellow-400 font-bold ml-2">
              🎉 Election Winner!
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};
