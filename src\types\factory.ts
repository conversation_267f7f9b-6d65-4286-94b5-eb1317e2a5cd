export enum FactoryType {
  GOLD = 'GOLD',
  MONEY = 'MONEY'
}

export enum WageType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED'
}

export interface Factory {
  id: number;
  name: string;
  type: FactoryType;
  regionId: string;
  ownerId: number | null;
  wage: number;
  wageType: WageType;
  
  // New leveling system
  level: number;
  experience: number;
  creationCost: number;
  totalUpgradeCost: number;
  resourceBonus: number; // Percentage bonus from leveling
  
  createdAt: string;
  updatedAt: string;
  owner?: {
    id: number;
    username: string;
  };
  region?: {
    id: string;
    name: string;
  };
  workers?: any[];
  factoryWorkers?: FactoryWorker[];
}

export interface FactoryWorker {
  id: number;
  factoryId: number;
  workerId: number;
  wage: number;
  wageType: WageType;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  worker?: {
    id: number;
    username: string;
  };
}

export interface WorkSession {
  id: number;
  factoryId: number;
  workerId: number;
  energySpent: number;
  wageEarned: number;
  resourceEarned: number;
  efficiencyMultiplier: number;
  resourceType: FactoryType;
  createdAt: string;
  updatedAt: string;
  factory?: Factory;
  worker?: {
    id: number;
    username: string;
  };
}

export interface FactoryUpgradeResponse {
  factory: Factory;
  level: number;
  experience: number;
  experienceProgress: number;
  requiredExperience: number;
  canUpgrade: boolean;
  upgradeCost: number;
}

export interface FactoryShutdownResponse {
  message: string;
  refundAmount: number;
  workersFired: number;
}

export interface FireWorkerResponse {
  message: string;
  workerFired: boolean;
}