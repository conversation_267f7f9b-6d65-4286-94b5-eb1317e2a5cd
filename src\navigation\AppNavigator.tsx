import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Text, View } from "react-native";

import { useAuthStore } from "../store/useAuthStore";
import { LoginScreen } from "../screens/LoginScreen";
import { RegisterScreen } from "../screens/RegisterScreen";
import { ForgotPasswordScreen } from "../screens/ForgotPasswordScreen";
import { ResetPasswordScreen } from "../screens/ResetPasswordScreen";
import { HomeScreen } from "../screens/HomeScreen";
import { ProfileScreen } from "../screens/ProfileScreen";
import { MapScreen } from "../screens/MapScreen";
import { WarsScreen } from "../screens/WarsScreen";
import { WarDetailScreen } from "../screens/WarDetailScreen";
import { DeclareWarScreen } from "../screens/DeclareWarScreen";
import { FactoriesScreen } from "../screens/FactoriesScreen";
import { ChatListScreen } from "../screens/ChatListScreen";
import { ElectionsScreen } from "../screens/ElectionsScreen";
import { ShopScreen } from "../screens/ShopScreen";
import { NetworkDebugScreen } from "../screens/NetworkDebugScreen";
import { MoreTabScreen } from "../screens/MoreTabScreen";
import { LandingScreen } from "../screens/LandingScreen";
import { RegionDetailScreen } from "../screens/RegionDetailScreen";
import { StateBudgetScreen } from "../screens/StateBudgetScreen";
import { TravelPermissionsScreen } from "../screens/TravelPermissionsScreen";
import { UserProfileScreen } from "../screens/UserProfileScreen";
import { VerifyAccountScreen } from "../screens/VerifyAccountScreen";
import { CreatePartyScreen } from "../screens/CreatePartyScreen";
import { PartyDetailScreen } from "../screens/PartyDetailScreen";
import { PrivacyPolicyScreen } from "../screens/PrivacyPolicyScreen";
import { TermsOfServiceScreen } from "../screens/TermsOfServiceScreen";
import { JobsScreen } from "../screens/JobsScreen";
import { MyStateScreen } from "../screens/MyStateScreen";
import { PaymentSuccessScreen } from "../screens/PaymentSuccessScreen";
import { PaymentCancelScreen } from "../screens/PaymentCancelScreen";
import { WarAnalyticsScreen } from "../screens/WarAnalyticsScreen";
import { NotFoundScreen } from "../screens/NotFoundScreen";

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const AuthStack = () => (
  <Stack.Navigator
    screenOptions={{
      cardStyle: {
        flex: 1,
      },
      headerStyle: {
        backgroundColor: "#1a1a2e",
      },
      headerTintColor: "#00d4ff",
      headerTitleStyle: {
        fontWeight: "bold",
      },
    }}
  >
    <Stack.Screen
      name="Landing"
      component={LandingScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="Login"
      component={LoginScreen}
      options={{ title: "Login" }}
    />
    <Stack.Screen
      name="Register"
      component={RegisterScreen}
      options={{ title: "Create Account" }}
    />
    <Stack.Screen
      name="ForgotPassword"
      component={ForgotPasswordScreen}
      options={{ title: "Forgot Password" }}
    />
    <Stack.Screen
      name="ResetPassword"
      component={ResetPasswordScreen}
      options={{ title: "Reset Password" }}
    />
  </Stack.Navigator>
);

const MainTabs = () => (
  <Tab.Navigator
    screenOptions={{
      tabBarStyle: {
        backgroundColor: "#16213e",
        borderTopColor: "#0f3460",
        borderTopWidth: 1,
      },
      tabBarActiveTintColor: "#00d4ff",
      tabBarInactiveTintColor: "#ccc",
      headerStyle: {
        backgroundColor: "#1a1a2e",
      },
      headerTintColor: "#00d4ff",
      headerTitleStyle: {
        fontWeight: "bold",
      },
    }}
  >
    <Tab.Screen
      name="Home"
      component={HomeScreen}
      options={{
        title: "Home",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>🏠</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Wars"
      component={WarsScreen}
      options={{
        title: "Wars",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>⚔️</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Factories"
      component={FactoriesScreen}
      options={{
        title: "Factories",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>🏭</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Shop"
      component={ShopScreen}
      options={{
        title: "Shop",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>🛒</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Map"
      component={MapScreen}
      options={{
        title: "Map",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>🗺️</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Profile"
      component={ProfileScreen}
      options={{
        title: "Profile",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>👤</Text>
        ),
      }}
    />
    <Tab.Screen
      name="More"
      component={MoreTabScreen}
      options={{
        title: "More",
        tabBarIcon: ({ focused }) => (
          <Text style={{ color: focused ? "#00d4ff" : "#ccc" }}>⋯</Text>
        ),
      }}
    />
  </Tab.Navigator>
);

const MainStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: "#1a1a2e",
      },
      headerTintColor: "#00d4ff",
      headerTitleStyle: {
        fontWeight: "bold",
      },
    }}
  >
    <Stack.Screen
      name="MainTabs"
      component={MainTabs}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="WorldMap"
      component={MapScreen}
      options={{ title: "World Map" }}
    />
    <Stack.Screen
      name="WarDetail"
      component={WarDetailScreen}
      options={{ title: "War Details" }}
    />
    <Stack.Screen
      name="DeclareWar"
      component={DeclareWarScreen}
      options={{ title: "Declare War" }}
    />
    <Stack.Screen
      name="Elections"
      component={ElectionsScreen}
      options={{ title: "Elections" }}
    />
    <Stack.Screen
      name="NetworkDebug"
      component={NetworkDebugScreen}
      options={{ title: "Network Debug" }}
    />
    <Stack.Screen
      name="Regions"
      component={RegionDetailScreen}
      options={{ title: "Region Details" }}
    />
    <Stack.Screen
      name="MyState"
      component={MyStateScreen}
      options={{ title: "My State" }}
    />
    <Stack.Screen
      name="Travel"
      component={TravelPermissionsScreen}
      options={{ title: "Travel Permissions" }}
    />
    <Stack.Screen
      name="UserProfile"
      component={UserProfileScreen}
      options={{ title: "User Profile" }}
    />
    <Stack.Screen
      name="VerifyAccount"
      component={VerifyAccountScreen}
      options={{ title: "Verify Account" }}
    />
    <Stack.Screen
      name="CreateState"
      component={StateBudgetScreen}
      options={{ title: "Create State" }}
    />
    <Stack.Screen
      name="Jobs"
      component={JobsScreen}
      options={{ title: "Jobs" }}
    />
    <Stack.Screen
      name="CreateParty"
      component={CreatePartyScreen}
      options={{ title: "Create Party" }}
    />
    <Stack.Screen
      name="PartyDetail"
      component={PartyDetailScreen}
      options={{ title: "Party Details", headerShown: false }}
    />
    <Stack.Screen
      name="PrivacyPolicy"
      component={PrivacyPolicyScreen}
      options={{ title: "Privacy Policy", headerShown: false }}
    />
    <Stack.Screen
      name="TermsOfService"
      component={TermsOfServiceScreen}
      options={{ title: "Terms of Service", headerShown: false }}
    />
    <Stack.Screen
      name="PaymentSuccess"
      component={PaymentSuccessScreen}
      options={{ title: "Payment Success", headerShown: false }}
    />
    <Stack.Screen
      name="PaymentCancel"
      component={PaymentCancelScreen}
      options={{ title: "Payment Cancelled", headerShown: false }}
    />
    <Stack.Screen
      name="WarAnalytics"
      component={WarAnalyticsScreen}
      options={{ title: "War Analytics" }}
    />
    <Stack.Screen
      name="NotFound"
      component={NotFoundScreen}
      options={{ title: "Page Not Found", headerShown: false }}
    />
    <Stack.Screen
      name="Messages"
      component={ChatListScreen}
      options={{ title: "Messages" }}
    />
    <Stack.Screen
      name="RegionDetail"
      component={RegionDetailScreen}
      options={{ title: "Region Details" }}
    />
  </Stack.Navigator>
);

export const AppNavigator = () => {
  const { isAuthenticated, loadToken, isLoading } = useAuthStore();

  useEffect(() => {
    loadToken();
  }, []);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0a0c1b' }}>
        <Text style={{ color: '#00d4ff', fontSize: 18, fontWeight: 'bold' }}>Loading...</Text>
      </View>
    );
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? <MainStack /> : <AuthStack />}
    </NavigationContainer>
  );
};
