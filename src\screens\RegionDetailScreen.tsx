import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  MapPin,
  Users,
  DollarSign,
  Building,
  Shield,
  Sword,
  Crown,
  ArrowLeft,
  Activity,
  TrendingUp,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { stateService } from '../services/stateService';
import { regionService } from '../services/regionService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { Region } from '../types/region';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';
import { generateStateColor } from '../utils/colorGenerator';
import { ProgressBar } from '../components/ProgressIndicator';

interface Props {
  navigation: any;
  route: any;
}



export const RegionDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { regionId, stateId } = route.params;
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [region, setRegion] = useState<Region | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'economy' | 'military' | 'politics'>('overview');

  useEffect(() => {
    loadRegionDetail();
  }, [regionId]);

  const loadRegionDetail = async () => {
    try {
      setLoading(true);
      // Load actual region data from API
      const regionData = await regionService.getRegionById(regionId);

      setRegion(regionData);
    } catch (error) {
      console.error('Error loading region detail:', error);
      showErrorToast('Failed to load region details');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadRegionDetail();
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderTabButton = (tab: string, icon: any, label: string) => {
    const IconComponent = icon;
    const isActive = activeTab === tab;
    
    return (
      <TouchableOpacity
        key={tab}
        onPress={() => setActiveTab(tab as any)}
        style={{
          paddingVertical: 12,
          paddingHorizontal: 16,
          borderBottomWidth: isActive ? 2 : 0,
          borderBottomColor: '#3f87ff',
          flex: 1,
          alignItems: 'center',
        }}
      >
        <IconComponent width={20} height={20} color={isActive ? '#3f87ff' : '#9CA3AF'} />
        <Text style={{
          color: isActive ? '#3f87ff' : '#9CA3AF',
          fontSize: 12,
          fontWeight: isActive ? '600' : '400',
          marginTop: 4,
        }}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderOverviewTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Region Overview
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Users width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Population
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {region?.population.toLocaleString()}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Total residents
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Player Activity
            </Text>
          </View>
          <ProgressBar
            progress={region?.developmentIndex || 0}
            progressColor="#10B981"
            showPercentage={true}
          />
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Shield width={20} height={20} color={generateStateColor(0)} />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              State Information
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600' }}>
            {region?.state?.name || 'Independent'}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 2 }}>
            Status: {region?.status || 'Unknown'}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderEconomyTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Economic Status
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <DollarSign width={20} height={20} color="#F59E0B" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Regional GDP
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {formatNumber(region?.developmentIndex || 0)}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Development Index
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Building width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {region?.factories?.length || 0}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Total Factories</Text>
          </View>
          
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {region?.factories?.length || 0}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Active Factories</Text>
          </View>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <TrendingUp width={20} height={20} color="#EF4444" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Tax Rate
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {region?.taxRate || 0}%
          </Text>
          <ProgressBar
            progress={region?.taxRate || 0}
            progressColor="#EF4444"
            height={6}
          />
        </View>
      </View>
    </View>
  );

  const renderMilitaryTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Military Status
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Sword width={20} height={20} color="#EF4444" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Total Military Force
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {formatNumber(region?.militaryIndex || 0)}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Military Index
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {formatNumber(region?.initialAttackDamage || 0)}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Attack Damage</Text>
          </View>
          
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Shield width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {region?.initialDefendDamage}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Defense Damage</Text>
          </View>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Shield width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Defense Readiness
            </Text>
          </View>
          <ProgressBar
            progress={region?.militaryIndex || 0}
            progressColor="#3f87ff"
            height={8}
            showPercentage={true}
          />
          <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>
            Military preparedness level
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Troop Deployment
            </Text>
          </View>
          <View style={{ marginBottom: 8 }}>
            <Text style={{ color: '#9CA3AF', fontSize: 12, marginBottom: 4 }}>
              Health Index: {region?.healthIndex || 0}%
            </Text>
            <ProgressBar
              progress={region?.healthIndex || 0}
              progressColor="#10B981"
              height={6}
            />
          </View>
        </View>
      </View>
    </View>
  );

  const renderPoliticsTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Political Status
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Crown width={20} height={20} color="#F59E0B" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Governing State
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold' }}>
            {region?.state?.name || 'Independent'}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Current controlling state
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Users width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              State Leadership
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600' }}>
            {region?.state?.leader?.username || 'None'}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 2 }}>
            Current state leader
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Political Stability
            </Text>
          </View>
          <ProgressBar
            progress={75}
            progressColor="#10B981"
            height={8}
            showPercentage={true}
          />
          <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>
            Regional political stability rating
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Shield width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Governance Information
            </Text>
          </View>
          <View style={{ gap: 8 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF', fontSize: 14 }}>Government Type:</Text>
              <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>Republic</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF', fontSize: 14 }}>Tax Rate:</Text>
              <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>15%</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF', fontSize: 14 }}>Public Support:</Text>
              <Text style={{ color: '#10B981', fontSize: 14, fontWeight: '500' }}>High</Text>
            </View>
          </View>
        </View>

        {region?.parties && region.parties.length > 0 && (
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Crown width={20} height={20} color="#F59E0B" />
              <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
                Governorship Candidates
              </Text>
            </View>
            <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
              {region.parties.length} political parties active in this region
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: '#111827',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#9CA3AF', fontSize: 16, marginTop: 16 }}>
          Loading region details...
        </Text>
      </View>
    );
  }

  if (!region) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: '#111827',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
        <MapPin width={48} height={48} color="#6B7280" />
        <Text style={{ color: '#9CA3AF', fontSize: 18, marginTop: 16, textAlign: 'center' }}>
          Region not found
        </Text>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            backgroundColor: '#3f87ff',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 6,
            marginTop: 16,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 border-b border-gray-700">
        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3">
          <ArrowLeft width={24} height={24} color="#3f87ff" />
        </TouchableOpacity>
        <MapPin width={24} height={24} color="#3f87ff" />
        <Text className="text-white text-xl font-bold ml-2">
          {region.name}
        </Text>
      </View>

      {/* Tabs */}
      <View style={{ 
        flexDirection: 'row', 
        backgroundColor: '#1F2937', 
        borderBottomWidth: 1, 
        borderBottomColor: '#374151' 
      }}>
        {renderTabButton('overview', Activity, 'Overview')}
        {renderTabButton('economy', DollarSign, 'Economy')}
        {renderTabButton('military', Shield, 'Military')}
        {renderTabButton('politics', Crown, 'Politics')}
      </View>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'economy' && renderEconomyTab()}
        {activeTab === 'military' && renderMilitaryTab()}
        {activeTab === 'politics' && renderPoliticsTab()}
      </ScrollView>
    </View>
  );
};