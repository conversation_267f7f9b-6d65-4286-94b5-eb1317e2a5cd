import { create } from 'zustand';
import { userService } from '../services/userService';
import { useAuthStore } from './useAuthStore';

interface UserData {
  id: number;
  username: string;
  email: string;
  level: number;
  experience: number;
  strength: number;
  defense: number;
  intelligence: number;
  gold: number;
  energy: number;
  food: number;
  isPremium: boolean;
  premiumExpiresAt?: string;
  subscriptionStatus?: string;
  avatarUrl?: string;
  region?: {
    id: string;
    name: string;
    population?: number;
    developmentIndex?: number;
    state?: {
      id: string;
      name: string;
    };
  };
  memberOfParty?: {
    id: string;
    name: string;
    leaderId: number;
    region?: {
      id: string;
      name: string;
    };
  };
  leadingParty?: {
    id: string;
    name: string;
    memberCount: number;
    maxMembers: number;
  };
  trainingExpiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserDataStore {
  userData: UserData | null;
  loading: boolean;
  error: string | null;
  fetchUserData: (force?: boolean) => Promise<UserData | null>;
  updateUserData: (newData: Partial<UserData>) => void;
  clearUserData: () => void;
}

// Simplified caching: always return cached data unless explicitly forced.
export const useUserDataStore = create<UserDataStore>((set, get) => ({
  userData: null,
  loading: false,
  error: null,

  fetchUserData: async (force = false) => {
    const { user } = useAuthStore.getState();
    if (!user?.id) return null;

    const state = get();

    // Return cached data if present and not forcing
    if (state.userData && !force) {
      return state.userData;
    }

    // If we don't have userData yet but have user, use it
    if (user && !force) {
      const userData: UserData = {
        ...user,
        defense: user.endurance || 0, // Map endurance to defense
        food: 0, // Default food value
      };
      set({ userData });
      return userData;
    }

    // If a request is already in progress (and not forcing), wait for it
    if (state.loading && !force) {
      return new Promise<UserData | null>((resolve) => {
        const checkLoading = () => {
          const { loading, userData } = get();
          if (!loading) {
            resolve(userData);
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    // Otherwise, fetch fresh data
    set({ loading: true });
    try {
      // Use getCurrentUser since getProfile doesn't exist yet
      const response = await userService.getCurrentUser();
      const userData: UserData = {
        ...response,
        defense: response.endurance || 0, // Map endurance to defense
        food: 0, // Default food value
      };
      set({
        userData,
        loading: false,
        error: null
      });
      return userData;
    } catch (error: any) {
      console.error('Failed to fetch user data:', error);

      // If API call fails, at least use the basic user data we have
      if (user) {
        const userData: UserData = {
          ...user,
          defense: user.endurance || 0, // Map endurance to defense
          food: 0, // Default food value
        };
        set({
          userData,
          error: 'Failed to fetch detailed user data',
          loading: false
        });
        return userData;
      } else {
        set({ error: 'Failed to fetch user data', loading: false });
        return null;
      }
    }
  },

  updateUserData: (newData: Partial<UserData>) => {
    set((state) => ({
      userData: state.userData ? {
        ...state.userData,
        ...newData
      } : newData as UserData
    }));
  },

  // Clear user data (useful for logout)
  clearUserData: () => {
    set({
      userData: null,
      loading: false,
      error: null
    });
  }
}));
