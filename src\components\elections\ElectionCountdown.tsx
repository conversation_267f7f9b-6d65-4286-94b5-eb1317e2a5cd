import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
} from 'react-native';
import { Clock } from 'lucide-react-native';

interface ElectionCountdownProps {
  endDate: string;
  onExpired?: () => void;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  expired: boolean;
}

export const ElectionCountdown: React.FC<ElectionCountdownProps> = ({ 
  endDate, 
  onExpired 
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    expired: false,
  });

  const calculateTimeRemaining = (endDate: string): TimeRemaining => {
    const now = new Date().getTime();
    const end = new Date(endDate).getTime();
    const difference = end - now;

    if (difference <= 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        expired: true,
      };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return {
      days,
      hours,
      minutes,
      seconds,
      expired: false,
    };
  };

  useEffect(() => {
    const updateCountdown = () => {
      const remaining = calculateTimeRemaining(endDate);
      setTimeRemaining(remaining);

      if (remaining.expired && onExpired) {
        onExpired();
      }
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [endDate, onExpired]);

  if (timeRemaining.expired) {
    return (
      <View className="bg-red-900 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg p-4">
        <View className="flex-row items-center justify-center">
          <Clock width={20} height={20} color="#ef4444" />
          <Text className="text-red-400 font-bold ml-2">Election Ended</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="bg-blue-900 bg-opacity-20 border border-blue-400 border-opacity-30 rounded-lg p-4">
      <View className="flex-row items-center justify-center mb-3">
        <Clock width={20} height={20} color="#60a5fa" />
        <Text className="text-blue-400 font-medium ml-2">Time Remaining</Text>
      </View>
      
      <View className="flex-row justify-center space-x-4">
        {timeRemaining.days > 0 && (
          <View className="items-center">
            <Text className="text-2xl font-bold text-white">{timeRemaining.days}</Text>
            <Text className="text-gray-400 text-sm">
              {timeRemaining.days === 1 ? 'Day' : 'Days'}
            </Text>
          </View>
        )}
        
        <View className="items-center">
          <Text className="text-2xl font-bold text-white">
            {timeRemaining.hours.toString().padStart(2, '0')}
          </Text>
          <Text className="text-gray-400 text-sm">Hours</Text>
        </View>
        
        <View className="items-center">
          <Text className="text-2xl font-bold text-white">
            {timeRemaining.minutes.toString().padStart(2, '0')}
          </Text>
          <Text className="text-gray-400 text-sm">Minutes</Text>
        </View>
        
        <View className="items-center">
          <Text className="text-2xl font-bold text-white">
            {timeRemaining.seconds.toString().padStart(2, '0')}
          </Text>
          <Text className="text-gray-400 text-sm">Seconds</Text>
        </View>
      </View>
      
      {timeRemaining.days === 0 && timeRemaining.hours < 1 && (
        <View className="mt-3 bg-yellow-900 bg-opacity-30 rounded-lg p-2">
          <Text className="text-yellow-400 text-center text-sm font-medium">
            ⚠️ Election ending soon!
          </Text>
        </View>
      )}
    </View>
  );
};
