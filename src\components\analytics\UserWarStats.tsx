import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {
  Sword,
  Trophy,
  X,
  Flame,
  Target,
  Zap,
  TrendingUp,
} from 'lucide-react-native';
import { warService } from '../../services/warService';
import { userService } from '../../services/userService';
import { showErrorToast } from '../../utils/toastUtils';

interface UserWarStatistics {
  totalParticipation: number;
  warsWon: number;
  warsLost: number;
  totalDamageDealt: number;
  highestDamageInSingleWar: number;
  mostActiveWarId: string;
  currentActiveWars: number;
}

interface UserWarStatsProps {
  userId?: string | number;
  navigation?: any;
}

export const UserWarStats: React.FC<UserWarStatsProps> = ({ userId, navigation }) => {
  const [stats, setStats] = useState<UserWarStatistics | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [username, setUsername] = useState<string>('');

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        setLoading(true);

        // If userId is provided, fetch that user's stats, otherwise get current user's stats
        let data: UserWarStatistics;
        if (userId) {
          // Get the username for display
          try {
            const userData = await userService.getUserById(userId);
            setUsername(userData?.username || '');
          } catch (error) {
            console.error('Failed to fetch username:', error);
          }

          // Use the userService to get war analytics for the specific user
          try {
            const analytics = await userService.getUserWarAnalytics(Number(userId));
            // Transform analytics to match our UserWarStatistics interface
            data = {
              totalParticipation: analytics.totalWars || 0,
              warsWon: analytics.warsWon || 0,
              warsLost: analytics.warsLost || 0,
              totalDamageDealt: analytics.totalDamage || 0,
              highestDamageInSingleWar: analytics.highestDamage || 0,
              mostActiveWarId: analytics.mostActiveWarId || '',
              currentActiveWars: analytics.activeWars || 0,
            };
          } catch (error) {
            // Fallback to mock data if endpoint doesn't exist yet
            console.error('Failed to fetch specific user war stats, using mock data:', error);
            data = {
              totalParticipation: 0,
              warsWon: 0,
              warsLost: 0,
              totalDamageDealt: 0,
              highestDamageInSingleWar: 0,
              mostActiveWarId: '',
              currentActiveWars: 0,
            };
          }
        } else {
          // For current user, use mock data until getUserWarStats is implemented
          data = {
            totalParticipation: 0,
            warsWon: 0,
            warsLost: 0,
            totalDamageDealt: 0,
            highestDamageInSingleWar: 0,
            mostActiveWarId: '',
            currentActiveWars: 0,
          };
        }

        setStats(data);
      } catch (error) {
        console.error('Failed to fetch user war statistics:', error);
        showErrorToast('Failed to load war statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchUserStats();
  }, [userId]);

  if (loading) {
    return (
      <View className="p-4">
        <View className="flex-row items-center justify-center py-8">
          <ActivityIndicator size="large" color="#00d4ff" />
          <Text className="text-gray-400 ml-3">Loading war statistics...</Text>
        </View>
      </View>
    );
  }

  if (!stats) {
    return (
      <View className="p-4">
        <Text className="text-gray-400 text-center">No war statistics available</Text>
      </View>
    );
  }

  const winRate = stats.totalParticipation > 0 
    ? ((stats.warsWon / stats.totalParticipation) * 100).toFixed(1)
    : '0.0';

  const averageDamage = stats.totalParticipation > 0
    ? (stats.totalDamageDealt / stats.totalParticipation).toFixed(0)
    : '0';

  return (
    <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
      <Text className="text-xl font-bold text-white mb-4">
        {userId ? `${username ? `${username}'s` : 'User'} War Statistics` : 'Your War Statistics'}
      </Text>

      <View className="flex-row flex-wrap justify-between mb-6">
        {/* Total Wars */}
        <View className="bg-gray-700 p-4 rounded-lg mb-4 w-[48%]">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-gray-400 text-sm">Total Wars</Text>
            <Sword width={20} height={20} color="#00d4ff" />
          </View>
          <Text className="text-2xl font-bold text-white">{stats.totalParticipation}</Text>
        </View>

        {/* Wars Won */}
        <View className="bg-gray-700 p-4 rounded-lg mb-4 w-[48%]">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-gray-400 text-sm">Wars Won</Text>
            <Trophy width={20} height={20} color="#10b981" />
          </View>
          <Text className="text-2xl font-bold text-white">{stats.warsWon}</Text>
        </View>

        {/* Wars Lost */}
        <View className="bg-gray-700 p-4 rounded-lg mb-4 w-[48%]">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-gray-400 text-sm">Wars Lost</Text>
            <X width={20} height={20} color="#ef4444" />
          </View>
          <Text className="text-2xl font-bold text-white">{stats.warsLost}</Text>
        </View>

        {/* Total Damage */}
        <View className="bg-gray-700 p-4 rounded-lg mb-4 w-[48%]">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-gray-400 text-sm">Total Damage</Text>
            <Flame width={20} height={20} color="#f59e0b" />
          </View>
          <Text className="text-2xl font-bold text-white">{stats.totalDamageDealt.toLocaleString()}</Text>
        </View>

        {/* Highest Damage */}
        <View className="bg-gray-700 p-4 rounded-lg mb-4 w-[48%]">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-gray-400 text-sm">Highest Damage</Text>
            <Target width={20} height={20} color="#f97316" />
          </View>
          <Text className="text-2xl font-bold text-white">{stats.highestDamageInSingleWar.toLocaleString()}</Text>
        </View>

        {/* Active Wars */}
        <View className="bg-gray-700 p-4 rounded-lg mb-4 w-[48%]">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-gray-400 text-sm">Active Wars</Text>
            <Zap width={20} height={20} color="#8b5cf6" />
          </View>
          <Text className="text-2xl font-bold text-white">{stats.currentActiveWars}</Text>
        </View>
      </View>

      {/* Most Active War */}
      {stats.mostActiveWarId && navigation && (
        <View className="bg-gray-700 p-4 rounded-lg mb-4">
          <Text className="text-lg font-medium text-white mb-2">Most Active War</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('WarDetail', { warId: stats.mostActiveWarId })}
            className="bg-blue-600 py-2 px-4 rounded-lg"
          >
            <Text className="text-white text-center font-medium">View War Details</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Win Rate Progress Bar */}
      <View className="mt-4">
        <View className="flex-row items-center justify-between mb-2">
          <Text className="text-lg font-medium text-white">Win Rate</Text>
          <Text className="text-white font-bold">{winRate}%</Text>
        </View>
        <View className="h-3 bg-gray-600 rounded-full overflow-hidden">
          <View
            className="h-full bg-gradient-to-r from-green-500 to-emerald-500"
            style={{ width: `${winRate}%` }}
          />
        </View>
      </View>

      {/* Average Damage */}
      <View className="mt-4">
        <View className="flex-row items-center justify-between">
          <Text className="text-gray-400">Average Damage per War</Text>
          <Text className="text-white font-bold">{averageDamage}</Text>
        </View>
      </View>

      {/* Efficiency Indicator */}
      {stats.totalParticipation > 0 && (
        <View className="mt-4 bg-gray-700 p-3 rounded-lg">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <TrendingUp width={16} height={16} color="#10b981" />
              <Text className="text-gray-400 ml-2">War Efficiency</Text>
            </View>
            <Text className="text-green-400 font-bold">
              {((stats.warsWon / stats.totalParticipation) * 100).toFixed(1)}%
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};
