import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {
  X,
  DollarSign,
  Zap,
  TrendingUp,
  Calculator,
} from 'lucide-react-native';

interface WorkSession {
  wageEarned: number;
  resourceEarned: number;
  resourceType: 'GOLD' | 'MONEY';
  energySpent: number;
  efficiencyMultiplier: number;
  grossWage?: number;
  incomeTax?: number;
  taxRate?: number;
  stateName?: string;
  showTaxBreakdown?: boolean;
}

interface WorkResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  workSession: WorkSession | null;
}

export const WorkResultModal: React.FC<WorkResultModalProps> = ({
  isOpen,
  onClose,
  workSession,
}) => {
  if (!isOpen || !workSession) return null;

  return (
    <Modal
      visible={isOpen}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black bg-opacity-50 justify-center items-center">
        <View className="bg-gray-800 rounded-xl p-6 max-w-sm w-full mx-4 border border-gray-700">
          {/* Header */}
          <View className="flex-row items-center justify-between mb-6">
            <Text className="text-xl font-bold text-white">Work Results</Text>
            <TouchableOpacity onPress={onClose} className="p-1">
              <X width={24} height={24} color="#ffffff" />
            </TouchableOpacity>
          </View>

          <ScrollView className="max-h-96">
            <View className="space-y-4">
              {/* Tax breakdown (if applicable) */}
              {workSession.showTaxBreakdown && 
               workSession.grossWage && 
               workSession.incomeTax && 
               workSession.incomeTax > 0 && (
                <View className="bg-blue-900 bg-opacity-20 border border-blue-400 border-opacity-30 rounded-lg p-4">
                  <View className="flex-row items-center mb-3">
                    <Calculator width={16} height={16} color="#60a5fa" />
                    <Text className="text-blue-400 font-medium ml-2">Tax Breakdown</Text>
                  </View>
                  
                  <View className="space-y-2">
                    <View className="flex-row justify-between">
                      <Text className="text-gray-300 text-sm">Gross Wage:</Text>
                      <Text className="text-white font-medium">
                        ${workSession.grossWage.toLocaleString()}
                      </Text>
                    </View>
                    
                    <View className="flex-row justify-between">
                      <Text className="text-gray-300 text-sm">
                        Income Tax ({workSession.taxRate}%):
                      </Text>
                      <Text className="text-red-400 font-medium">
                        -${workSession.incomeTax.toLocaleString()}
                      </Text>
                    </View>
                    
                    {workSession.stateName && (
                      <Text className="text-gray-400 text-xs mt-1">
                        Paid to {workSession.stateName} treasury
                      </Text>
                    )}
                  </View>
                </View>
              )}

              {/* Money earned */}
              <View className="bg-gray-700 p-4 rounded-lg">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <DollarSign width={20} height={20} color="#10b981" />
                    <Text className="text-gray-300 ml-2">
                      {workSession.showTaxBreakdown ? 'Net Money Earned:' : 'Money Earned:'}
                    </Text>
                  </View>
                  <Text className="text-green-400 font-bold text-lg">
                    +${workSession.wageEarned.toLocaleString()}
                  </Text>
                </View>
              </View>

              {/* Resource earned */}
              <View className="bg-gray-700 p-4 rounded-lg">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <TrendingUp width={20} height={20} color={workSession.resourceType === 'GOLD' ? '#fbbf24' : '#10b981'} />
                    <Text className="text-gray-300 ml-2">{workSession.resourceType} Earned:</Text>
                  </View>
                  <Text className={`font-bold text-lg ${
                    workSession.resourceType === 'GOLD' ? 'text-yellow-400' : 'text-green-400'
                  }`}>
                    +{workSession.resourceEarned.toLocaleString()}
                  </Text>
                </View>
              </View>

              {/* Energy spent */}
              <View className="bg-gray-700 p-4 rounded-lg">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Zap width={20} height={20} color="#3b82f6" />
                    <Text className="text-gray-300 ml-2">Energy Spent:</Text>
                  </View>
                  <Text className="text-blue-400 font-bold text-lg">
                    {workSession.energySpent}
                  </Text>
                </View>
              </View>

              {/* Efficiency multiplier */}
              {workSession.efficiencyMultiplier && workSession.efficiencyMultiplier !== 1 && (
                <View className="bg-gray-700 p-4 rounded-lg">
                  <View className="flex-row items-center justify-between">
                    <Text className="text-gray-300">Efficiency Multiplier:</Text>
                    <Text className={`font-bold text-lg ${
                      workSession.efficiencyMultiplier > 1 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {workSession.efficiencyMultiplier.toFixed(2)}x
                    </Text>
                  </View>
                </View>
              )}

              {/* Summary */}
              <View className="bg-gradient-to-r from-green-900 to-blue-900 p-4 rounded-lg border border-green-400 border-opacity-30">
                <Text className="text-green-400 font-bold text-center text-lg mb-2">
                  Work Session Complete!
                </Text>
                <Text className="text-gray-300 text-center text-sm">
                  Great job! You've successfully completed your work session.
                </Text>
              </View>
            </View>
          </ScrollView>

          {/* Close button */}
          <TouchableOpacity
            onPress={onClose}
            className="bg-blue-600 py-3 rounded-lg mt-6"
          >
            <Text className="text-white font-bold text-center">Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default WorkResultModal;
