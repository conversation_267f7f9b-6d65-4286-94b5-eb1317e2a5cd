import { create } from 'zustand';
import { stateElectionService } from '../services/stateElectionService';
import { showErrorToast } from '../utils/toastUtils';
import { useUserDataStore } from './useUserDataStore';

interface Candidate {
  id: string;
  userId: number;
  user: {
    id: number;
    username: string;
    level?: number;
  };
  platform?: string;
  votes: number;
  createdAt: string;
}

interface Election {
  id: string;
  stateId: string;
  state?: {
    id: string;
    name: string;
  };
  status: 'active' | 'completed' | 'cancelled';
  startDate: string;
  endDate: string;
  candidates: Candidate[];
  totalVotes: number;
  winner?: Candidate;
  userHasVoted: boolean;
  userVote?: {
    candidateId: string;
    candidate: Candidate;
  };
  createdAt: string;
  updatedAt: string;
}

interface ElectionStore {
  activeElection: Election | null;
  electionHistory: Election[];
  loading: boolean;
  error: string | null;
  lastUpdated: number | null;
  historyPage: number;
  historyLimit: number;
  historyTotal: number;
  historyTotalPages: number;
  fetchActiveElection: (stateId: string, force?: boolean) => Promise<Election | null>;
  fetchElectionHistory: (stateId: string, page?: number, limit?: number) => Promise<any>;
  submitVote: (electionId: string, candidateId: string) => Promise<Election | null>;
  clearElectionData: () => void;
  setHistoryPage: (page: number) => void;
}

// Election configuration constants
const ELECTION_CONFIG = {
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
  },
};

// Utility function to normalize election data
const normalizeElectionData = (election: any, userData: any): Election => {
  if (!election) return election;

  // Check if user has voted
  const userHasVoted = election.votes?.some((vote: any) => vote.userId === userData?.id) || false;
  const userVote = election.votes?.find((vote: any) => vote.userId === userData?.id);

  return {
    ...election,
    userHasVoted,
    userVote: userVote ? {
      candidateId: userVote.candidateId,
      candidate: election.candidates?.find((c: any) => c.id === userVote.candidateId)
    } : undefined,
    totalVotes: election.votes?.length || 0,
  };
};

export const useElectionStore = create<ElectionStore>((set, get) => ({
  // State
  activeElection: null,
  electionHistory: [],
  loading: false,
  error: null,
  lastUpdated: null,

  // Pagination for history
  historyPage: ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
  historyLimit: ELECTION_CONFIG.PAGINATION.DEFAULT_LIMIT,
  historyTotal: 0,
  historyTotalPages: 0,

  // Actions
  fetchActiveElection: async (stateId: string, force = false) => {
    set({ loading: true, error: null });

    try {
      const election = await stateElectionService.getActiveElection(stateId);

      // Get user data for eligibility checking
      const userData = useUserDataStore.getState().userData;

      const normalizedElection = normalizeElectionData(election, userData);
      set({
        activeElection: normalizedElection,
        loading: false,
        lastUpdated: Date.now()
      });
      return normalizedElection;
    } catch (error: any) {
      console.error('Failed to fetch active election:', error);
      set({
        error: 'Failed to fetch active election',
        loading: false
      });
      showErrorToast('Failed to load election data');
      return null;
    }
  },

  fetchElectionHistory: async (stateId: string, page = 1, limit = 10) => {
    set({ loading: true, error: null });

    try {
      const history = await stateElectionService.getElectionHistory(stateId, page, limit);

      // Handle backend returning array directly vs wrapped object
      const electionsArray = Array.isArray(history) ? history : (history.elections || []);
      const userData = useUserDataStore.getState().userData;
      const normalizedElections = electionsArray.map((election: any) => normalizeElectionData(election, userData));

      // Calculate pagination info if not provided by backend
      const totalElections = Array.isArray(history) ? history.length : (history.total || normalizedElections.length);
      const totalPages = Math.ceil(totalElections / limit);

      set({
        electionHistory: normalizedElections,
        historyPage: Array.isArray(history) ? page : (history.page || page),
        historyLimit: Array.isArray(history) ? limit : (history.limit || limit),
        historyTotal: totalElections,
        historyTotalPages: totalPages,
        loading: false
      });
      return {
        elections: normalizedElections,
        page: page,
        limit: limit,
        total: totalElections,
        totalPages: totalPages
      };
    } catch (error: any) {
      console.error('Failed to fetch election history:', error);
      set({
        error: 'Failed to fetch election history',
        loading: false
      });
      showErrorToast('Failed to load election history');
      return null;
    }
  },

  submitVote: async (electionId: string, candidateId: string) => {
    set({ loading: true, error: null });

    try {
      const updatedElection = await stateElectionService.submitVote(electionId, { candidateId });
      const userData = useUserDataStore.getState().userData;
      const normalizedElection = normalizeElectionData(updatedElection, userData);
      set({
        activeElection: normalizedElection,
        loading: false,
        lastUpdated: Date.now()
      });
      return normalizedElection;
    } catch (error: any) {
      console.error('Failed to submit vote:', error);
      set({
        error: 'Failed to submit vote',
        loading: false
      });
      showErrorToast('Failed to submit vote');
      return null;
    }
  },

  clearElectionData: () => {
    set({
      activeElection: null,
      electionHistory: [],
      loading: false,
      error: null,
      lastUpdated: null,
      historyPage: ELECTION_CONFIG.PAGINATION.DEFAULT_PAGE,
      historyTotal: 0,
      historyTotalPages: 0,
    });
  },

  setHistoryPage: (page: number) => {
    set({ historyPage: page });
  },
}));
